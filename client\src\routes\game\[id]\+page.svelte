<script lang="ts">
  /**
   * Game Page Component
   *
   * This page accepts a "token" URL parameter containing a JWT with game session data:
   * - gameId: The game identifier
   * - roomId: Multiplayer room identifier
   * - scoreSubmitId: Unique ID for score submission tracking
   * - authToken: Authentication token for the game server
   *
   * Example URL: /game/[gameId]?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   */
  import { page } from "$app/state";

  import * as utilsModule from "$lib/utils";
  import * as socketModule from "$lib/socket";
  import * as gamesModule from "$lib/games";

  import Preloading from "$lib/components/Preloading.svelte";
  import StartScreen from "$lib/components/StartScreen.svelte";
  import GameHUD from "$lib/components/GameHUD.svelte";
  import Countdown from "$lib/components/Countdown.svelte";
  import EndGame from "$lib/components/EndGame.svelte";
  import ErrorModal from "$lib/components/ErrorModal.svelte";
  import { gameState, gameActions } from "$lib/stores";

  import type { SocketClient } from "$lib/socket";

  let gameInstance: any = $state(null);
  let gameContainer: HTMLDivElement | undefined = $state();
  let showCountdown = $state(false);
  let showEndGame = $state(false);
  let postMessageHandler: any = $state(null);
  let socketClient: SocketClient | null = $state(null);
  let createGame: any = $state(null);

  // Error modal state
  let showErrorModal = $state(false);
  let errorMessage = $state("");
  let errorType = $state("");

  const gameId = $derived(page.params.id);
  const token = $derived(page.url.searchParams.get("token"));
  let hasInitialized = false;

  $effect(() => {
    // This effect should only run once when the token is first available.
    if (token && !hasInitialized) {
      hasInitialized = true;

      console.log("Game ID:", gameId);
      console.log("Token:", token);
      console.log("JWT token received, will be validated by server");

      const currentToken = token;

      // Store the token for socket authentication
      gameActions.setAuthToken(currentToken);
      gameActions.setGameId(gameId);

      // Clear token from URL for security
      const url = new URL(window.location.href);
      url.searchParams.delete("token");
      history.replaceState({}, "", url);

      postMessageHandler = utilsModule.postMessageHandler;

      // Handle async operations in a separate function
      const initializeGame = async () => {
        try {
          socketClient = socketModule.socketClient;

          if (!socketClient) {
            console.error("Socket client not available");
            return;
          }

          await socketClient.connect(currentToken, {
            onScoreUpdate: (score: number) => {
              gameActions.updateScore(score);
            },
            onGameComplete: (_finalScore: number) => {
              gameActions.endGame();
              showEndGame = true;
            },
          });

          createGame = gamesModule.createGame(
            gameId,
            "game-container",
            socketClient
          );

          await createGame.init();
        } catch (error) {
          console.error("Failed to initialize game:", error);
        }
      };

      initializeGame();
    } else if (!token && !hasInitialized) {
      // This case handles development mode or if the token is missing on first load.
      console.log("No token provided - running in development mode");
    }

    // Cleanup function to run when the component is unmounted
    return () => {
      if (socketClient) {
        socketClient.disconnect();
      }
      if (gameInstance) {
        gameInstance.destroy();
      }
      gameActions.resetGame();
    };
  });

  async function handleStartGameButton() {
    console.log("Countdown complete");

    showCountdown = true;
    gameActions.initGame();
    createGame.start();
  }

  // Error modal functions
  function showError(message: string, type: string = "error") {
    errorMessage = message;
    errorType = type;
    showErrorModal = true;

    // Stop the game instance
    if (gameInstance) {
      gameInstance.scene.pause();
    }
  }

  function handleErrorClose() {
    // TODO: Post message trigeer back button
  }

  // Make error function globally available for Phaser scenes
  if (typeof window !== "undefined") {
    (window as any).showGameError = showError;
  }
</script>

<svelte:head>
  <title>TicTaps - {gameId}</title>
</svelte:head>

<Preloading progress={$gameState.loadingProgress} />

{#if !$gameState.isCountdown && !$gameState.isLoading}
  <StartScreen handleStartClick={handleStartGameButton} {gameId} />
{/if}

<div class="w-screen h-screen overflow-hidden relative">
  {#if $gameState.isPlaying}
    <GameHUD
      score={$gameState.score}
      time={$gameState.time}
      totalTime={$gameState.totalTime}
      lives={$gameState.lives}
      maxLives={$gameState.maxLives}
      opponentScore={$gameState.opponentScore}
      showOpponent={$gameState.opponentScore !== null}
    />
  {/if}

  <div
    class="w-full h-full box-border"
    bind:this={gameContainer}
    id="game-container"
  >
    <!-- games will be mounted here -->
  </div>

  <Countdown show={showCountdown} duration={3} />

  <EndGame show={$gameState.gameOver} finalScore={$gameState.score} />

  <ErrorModal
    isVisible={showErrorModal}
    {errorMessage}
    {errorType}
    onClose={handleErrorClose}
  />
</div>
